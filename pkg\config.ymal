app: # RESTful
  name: "wealth-bond-quote-service"
  mode: debug # server mode: release, debug, test
  addr: ":8081"
  ver: "0.0.1"
  prefork: false

log:
  path: "/data/logs/wealth-bond-quote-service/wealth-bond-quote-service.log"
  level: "debug"
  size: 1073741824
  count: 10

# 数据库配置
mysql:
  bondQuote:
    host: "localhost"
    port: 3306
    user: "root"
    password: "password"
    schema: "bond_quote_db"
    maxIdleConn: 5
    maxOpenConn: 20
    connMaxLifetime: 300 # 秒

# 亚丁ATS系统配置
adenATS:
  baseURL: "https://adenapi.cstm.adenfin.com"
  wssURL: "wss://adenapi.cstm.adenfin.com/message-gateway/message/atsapi/ws"
  username: "ATSTEST10001"
  password: "Abc12345"
  smsCode: "1234"
  clientId: "30021"
  publicKey: "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCTCY4u102mtUlVEyUMlXOkflPLdWN+ez5IDcLiNzw2ZkiEY17U4lk8iMx7yTEO/ZWCKIEdQV+U6tplJ98X3I/Py/DzWd1L7IPE6mZgclfcXg+P4ocaHPsKgAodc4G1W9jTu2d6obL3d33USCD0soGYE6fkf8hk7EPKhgNf4iUPCwIDAQAB"

# 数据处理配置
dataProcess:
  rawBufferSize: 20000
  parsedBufferSize: 4000
  workerNum: 8
  batchSize: 300
  flushDelayMs: 100

# 文件导出配置
export:
  path: "/data/export/bond_quote"
  urlPrefix: "http://localhost:8081/export"
  retentionDays: 7

# Fiber服务器配置
fiber:
  port: 8080
  readTimeout: 10
  writeTimeout: 30
  idleTimeout: 120
