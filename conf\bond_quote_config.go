package config

import (
	"sync"
)

// BondQuoteDBConfig 债券行情数据库配置
type BondQuoteDBConfig struct {
	Host            string `yaml:"host"`
	Port            int    `yaml:"port"`
	User            string `yaml:"user"`
	Password        string `yaml:"password"`
	Schema          string `yaml:"schema"`
	MaxIdleConn     int    `yaml:"maxIdleConn"`
	MaxOpenConn     int    `yaml:"maxOpenConn"`
	ConnMaxLifetime int    `yaml:"connMaxLifetime"`
}

// AdenATSConfig 亚丁ATS系统配置
type AdenATSConfig struct {
	BaseURL              string `yaml:"baseURL"`
	WssURL               string `yaml:"wssURL"`
	Username             string `yaml:"username"`
	Password             string `yaml:"password"`
	SmsCode              string `yaml:"smsCode"`
	ClientId             string `yaml:"clientId"`
	PublicKey            string `yaml:"publicKey"`
	Timeout              int    `yaml:"timeout"`              // 连接超时时间（秒）
	Heartbeat            int    `yaml:"heartbeat"`            // 心跳间隔（毫秒）
	ReconnectInterval    int    `yaml:"reconnectInterval"`    // 重连间隔（毫秒）
	MaxReconnectAttempts int    `yaml:"maxReconnectAttempts"` // 最大重连次数
}

// DataProcessConfig 数据处理配置
type DataProcessConfig struct {
	// 缓冲区配置
	RawBufferSize    int `yaml:"rawBufferSize"`    // 原始数据缓冲区大小
	ParsedBufferSize int `yaml:"parsedBufferSize"` // 解析后数据缓冲区大小
	DeadBufferSize   int `yaml:"deadBufferSize"`   // 死信队列缓冲区大小
	// 工作线程配置
	WorkerNum       int `yaml:"workerNum"`       // 数据处理工作线程数
	ParserWorkerNum int `yaml:"parserWorkerNum"` // 数据解析工作线程数
	DbWorkerNum     int `yaml:"dbWorkerNum"`     // 数据库写入工作线程数
	// 批处理配置
	BatchSize    int `yaml:"batchSize"`    // 批处理大小
	FlushDelayMs int `yaml:"flushDelayMs"` // 刷新延迟（毫秒）
	// 数据清理配置
	DataRetentionDays    int `yaml:"dataRetentionDays"`    // 数据保留天数
	CleanupIntervalHours int `yaml:"cleanupIntervalHours"` // 清理任务执行间隔（小时）
}

// MySQLDBPoolConfig MySQL数据库连接池配置
type MySQLDBPoolConfig struct {
	MaxIdleConn     int `yaml:"maxIdleConn"`
	MaxOpenConn     int `yaml:"maxOpenConn"`
	ConnMaxLifetime int `yaml:"connMaxLifetime"`
}

// MongoDBConfig MongoDB数据库配置
type MongoDBConfig struct {
	Host   string `yaml:"host"`
	Schema string `yaml:"schema"`
}

// MongoDBPoolConfig MongoDB连接池配置
type MongoDBPoolConfig struct {
	MaxConn int `yaml:"maxConn"`
	MinConn int `yaml:"minConn"`
	Timeout int `yaml:"timeout"`
}

// ExportConfig 文件导出配置
type ExportConfig struct {
	Path          string `yaml:"path"`
	URLPrefix     string `yaml:"urlPrefix"`
	RetentionDays int    `yaml:"retentionDays"`
}

// FiberConfig Fiber服务器配置
type FiberConfig struct {
	Port         int `yaml:"port"`
	ReadTimeout  int `yaml:"readTimeout"`
	WriteTimeout int `yaml:"writeTimeout"`
	IdleTimeout  int `yaml:"idleTimeout"`
}

var (
	bondQuoteDBConfig     *BondQuoteDBConfig
	onceBondQuoteDBConfig sync.Once

	adenATSConfig     *AdenATSConfig
	onceAdenATSConfig sync.Once

	dataProcessConfig     *DataProcessConfig
	onceDataProcessConfig sync.Once

	exportConfig     *ExportConfig
	onceExportConfig sync.Once

	fiberConfig     *FiberConfig
	onceFiberConfig sync.Once

	mysqlDBPoolConfig     *MySQLDBPoolConfig
	onceMySQLDBPoolConfig sync.Once

	mongoDBPoolConfig     *MongoDBPoolConfig
	onceMongoDBPoolConfig sync.Once

	wealthManageDBConfig     *MongoDBConfig
	onceWealthManageDBConfig sync.Once

	wealthManageDBRConfig     *MongoDBConfig
	onceWealthManageDBRConfig sync.Once
)

// GetBondQuoteDBConfig 获取债券行情数据库配置
func GetBondQuoteDBConfig() *BondQuoteDBConfig {
	onceBondQuoteDBConfig.Do(func() {
		bondQuoteDBConfig = &BondQuoteDBConfig{
			Host:            "localhost",
			Port:            3306,
			User:            "root",
			Password:        "password",
			Schema:          "bond_quote_db",
			MaxIdleConn:     5,
			MaxOpenConn:     20,
			ConnMaxLifetime: 300,
		}
		_ = GetCfg("mysql.bondQuote", bondQuoteDBConfig)
	})
	return bondQuoteDBConfig
}

// GetAdenATSConfig 获取亚丁ATS系统配置
func GetAdenATSConfig() *AdenATSConfig {
	onceAdenATSConfig.Do(func() {
		adenATSConfig = &AdenATSConfig{
			BaseURL:              "https://adenapi.cstm.adenfin.com",
			WssURL:               "wss://adenapi.cstm.adenfin.com/message-gateway/message/atsapi/ws",
			Username:             "ATSTEST10001",
			Password:             "Abc12345",
			SmsCode:              "1234",
			ClientId:             "30021",
			PublicKey:            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCTCY4u102mtUlVEyUMlXOkflPLdWN+ez5IDcLiNzw2ZkiEY17U4lk8iMx7yTEO/ZWCKIEdQV+U6tplJ98X3I/Py/DzWd1L7IPE6mZgclfcXg+P4ocaHPsKgAodc4G1W9jTu2d6obL3d33USCD0soGYE6fkf8hk7EPKhgNf4iUPCwIDAQAB",
			Timeout:              30,
			Heartbeat:            20000,
			ReconnectInterval:    5000,
			MaxReconnectAttempts: 10,
		}
		_ = GetCfg("adenATS", adenATSConfig)
	})
	return adenATSConfig
}

// GetDataProcessConfig 获取数据处理配置
func GetDataProcessConfig() *DataProcessConfig {
	onceDataProcessConfig.Do(func() {
		dataProcessConfig = &DataProcessConfig{
			RawBufferSize:        20000,
			ParsedBufferSize:     4000,
			DeadBufferSize:       1000,
			WorkerNum:            8,
			ParserWorkerNum:      4,
			DbWorkerNum:          2,
			BatchSize:            300,
			FlushDelayMs:         100,
			DataRetentionDays:    30,
			CleanupIntervalHours: 24,
		}
		_ = GetCfg("dataProcess", dataProcessConfig)
	})
	return dataProcessConfig
}

// GetExportConfig 获取文件导出配置
func GetExportConfig() *ExportConfig {
	onceExportConfig.Do(func() {
		exportConfig = &ExportConfig{
			Path:          "/data/export/bond_quote",
			URLPrefix:     "http://localhost:8081/download",
			RetentionDays: 7,
		}
		_ = GetCfg("export", exportConfig)
	})
	return exportConfig
}

// GetFiberConfig 获取Fiber服务器配置
func GetFiberConfig() *FiberConfig {
	onceFiberConfig.Do(func() {
		fiberConfig = &FiberConfig{
			Port:         8080,
			ReadTimeout:  10,
			WriteTimeout: 30,
			IdleTimeout:  120,
		}
		_ = GetCfg("fiber", fiberConfig)
	})
	return fiberConfig
}

// GetMySQLDBPoolConfig 获取MySQL数据库连接池配置
func GetMySQLDBPoolConfig() *MySQLDBPoolConfig {
	onceMySQLDBPoolConfig.Do(func() {
		mysqlDBPoolConfig = &MySQLDBPoolConfig{
			MaxIdleConn:     5,
			MaxOpenConn:     20,
			ConnMaxLifetime: 300,
		}
		_ = GetCfg("mysqlDBPool", mysqlDBPoolConfig)
	})
	return mysqlDBPoolConfig
}

// GetMongoDBPoolConfig 获取MongoDB连接池配置
func GetMongoDBPoolConfig() *MongoDBPoolConfig {
	onceMongoDBPoolConfig.Do(func() {
		mongoDBPoolConfig = &MongoDBPoolConfig{
			MaxConn: 30,
			MinConn: 2,
			Timeout: 300,
		}
		_ = GetCfg("mongoDBPool", mongoDBPoolConfig)
	})
	return mongoDBPoolConfig
}

// GetWealthManageDBConfig 获取财富管理数据库配置（读写）
func GetWealthManageDBConfig() *MongoDBConfig {
	onceWealthManageDBConfig.Do(func() {
		wealthManageDBConfig = &MongoDBConfig{
			Host:   "mongodb://localhost:27017",
			Schema: "wealthmanagedb",
		}
		_ = GetCfg("mongo.wealthmanagedb", wealthManageDBConfig)
	})
	return wealthManageDBConfig
}

// GetWealthManageDBRConfig 获取财富管理数据库配置（只读）
func GetWealthManageDBRConfig() *MongoDBConfig {
	onceWealthManageDBRConfig.Do(func() {
		wealthManageDBRConfig = &MongoDBConfig{
			Host:   "mongodb://localhost:27017",
			Schema: "wealthmanagedb",
		}
		_ = GetCfg("mongo.wealthmanagedb_r", wealthManageDBRConfig)
	})
	return wealthManageDBRConfig
}
