app: # RESTful
  name: "wealth-data-service v0.0.1"
  mode: debug # server mode: release, debug, test，
  addr: ":8081"
  ver: "0.0.1"
  prefork: false

apm:
  endpoint: 'http://tracing-analysis-dc-sz.aliyuncs.com/adapt_hvolair3j6@9a2ecfcb3f4fb6e_hvolair3j6@53df7ad2afe8301_sit/api/traces'

log:
  path: "/data/logs/wealth-data-service/wealth-data-service.log"
  level: "debug"
  size: 1073741824
  count: 10
mysql:
  fund:
    host: "**********"
    port: 3306
    password: QcXpaoKzN4AFGJ7MjxDv
    schema: funddb
    user: "hq_ro"
  symbol:
    host: "**********"
    port: 3306
    password: QcXpaoKzN4AFGJ7MjxDv
    schema: symboldb
    user: "hq_ro"
  bond:
    host: "**********"
    port: 3306
    password: QcXpaoKzN4AFGJ7MjxDv
    schema: bonddb
    user: "hq_ro"
  amount:
    host: "**********"
    port: 3306
    password: Jq4T3WYWgd52pRVY5qPG
    schema: bonddb
    user: "hq_rw"
  public:
    host: "**********"
    port: 3306
    password: QcXpaoKzN4AFGJ7MjxDv
    schema: publicdb
    user: "hq_ro"
mongo:
  wealthmanagedb:
    host: "*************************************************************************"
    schema: wealthmanagedb
  wealthmanagedb_r:
    host: "*************************************************************************"
    schema: wealthmanagedb
filePathPrefix: https://sit-hq-fund.oss-cn-shanghai.aliyuncs.com/
userHttpConf:
  url: http://innerapi-sit.fosunhanig.com/uc/v1/UserDetail
  timeout: 3
quoteDataService: http://mkt-data-service-go/
quoteUserService: http://mkt-user-service/
mysqlDBPool:
  maxIdleConn: 2
  maxOpenConn: 10
  connMaxLifetime: 300
mongoDBPool:
  maxConn: 30
  minConn: 2
  timeout: 300