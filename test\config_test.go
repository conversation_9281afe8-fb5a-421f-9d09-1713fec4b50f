package test

import (
	"testing"
	"test/conf"
)

// TestConfigInitialization 测试配置初始化
func TestConfigInitialization(t *testing.T) {
	// 初始化配置（仅使用本地配置，不依赖nacos）
	config.InitFromLocalFile("config", "yaml")
	
	// 测试数据库配置
	testDatabaseConfig(t)
	
	// 测试亚丁ATS配置
	testAdenATSConfig(t)
	
	// 测试数据处理配置
	testDataProcessConfig(t)
}

// testDatabaseConfig 测试数据库配置
func testDatabaseConfig(t *testing.T) {
	// 测试债券行情数据库配置
	bondQuoteDB := config.GetBondQuoteDBConfig()
	if bondQuoteDB == nil {
		t.Error("债券行情数据库配置为空")
		return
	}
	
	if bondQuoteDB.Host == "" {
		t.Error("数据库主机地址为空")
	}
	
	if bondQuoteDB.Port <= 0 {
		t.Error("数据库端口无效")
	}
	
	if bondQuoteDB.Schema == "" {
		t.Error("数据库名称为空")
	}
	
	t.Logf("债券行情数据库配置: %+v", bondQuoteDB)
	
	// 测试MySQL连接池配置
	mysqlPool := config.GetMySQLDBPoolConfig()
	if mysqlPool == nil {
		t.Error("MySQL连接池配置为空")
		return
	}
	
	if mysqlPool.MaxOpenConn <= 0 {
		t.Error("最大连接数配置无效")
	}
	
	t.Logf("MySQL连接池配置: %+v", mysqlPool)
	
	// 测试MongoDB配置
	wealthDB := config.GetWealthManageDBConfig()
	if wealthDB == nil {
		t.Error("财富管理数据库配置为空")
		return
	}
	
	if wealthDB.Host == "" {
		t.Error("MongoDB主机地址为空")
	}
	
	t.Logf("财富管理数据库配置: %+v", wealthDB)
}

// testAdenATSConfig 测试亚丁ATS配置
func testAdenATSConfig(t *testing.T) {
	adenATS := config.GetAdenATSConfig()
	if adenATS == nil {
		t.Error("亚丁ATS配置为空")
		return
	}
	
	if adenATS.BaseURL == "" {
		t.Error("ATS基础URL为空")
	}
	
	if adenATS.WssURL == "" {
		t.Error("WebSocket URL为空")
	}
	
	if adenATS.Username == "" {
		t.Error("用户名为空")
	}
	
	if adenATS.ClientId == "" {
		t.Error("客户端ID为空")
	}
	
	if adenATS.Timeout <= 0 {
		t.Error("连接超时时间配置无效")
	}
	
	if adenATS.Heartbeat <= 0 {
		t.Error("心跳间隔配置无效")
	}
	
	t.Logf("亚丁ATS配置: %+v", adenATS)
}

// testDataProcessConfig 测试数据处理配置
func testDataProcessConfig(t *testing.T) {
	dataProcess := config.GetDataProcessConfig()
	if dataProcess == nil {
		t.Error("数据处理配置为空")
		return
	}
	
	if dataProcess.RawBufferSize <= 0 {
		t.Error("原始数据缓冲区大小配置无效")
	}
	
	if dataProcess.ParsedBufferSize <= 0 {
		t.Error("解析后数据缓冲区大小配置无效")
	}
	
	if dataProcess.WorkerNum <= 0 {
		t.Error("工作线程数配置无效")
	}
	
	if dataProcess.BatchSize <= 0 {
		t.Error("批处理大小配置无效")
	}
	
	if dataProcess.DataRetentionDays <= 0 {
		t.Error("数据保留天数配置无效")
	}
	
	t.Logf("数据处理配置: %+v", dataProcess)
}

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	// 初始化配置
	config.InitFromLocalFile("config", "yaml")
	
	// 验证配置
	err := config.ValidateConfig()
	if err != nil {
		t.Errorf("配置验证失败: %v", err)
	}
}

// BenchmarkConfigAccess 配置访问性能测试
func BenchmarkConfigAccess(b *testing.B) {
	// 初始化配置
	config.InitFromLocalFile("config", "yaml")
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// 测试配置访问性能
		_ = config.GetBondQuoteDBConfig()
		_ = config.GetAdenATSConfig()
		_ = config.GetDataProcessConfig()
	}
}
