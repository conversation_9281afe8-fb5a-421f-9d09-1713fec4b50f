package test

import (
	config "test/conf"
	"testing"
)

// TestConfigInitialization 测试配置初始化
func TestConfigInitialization(t *testing.T) {
	// 初始化配置（仅使用本地配置，不依赖nacos）
	config.InitFromLocalFile("config", "yaml")

	// 测试亚丁ATS配置
	testAdenATSConfig(t)

	// 测试数据处理配置
	testDataProcessConfig(t)

	// 测试文件导出配置
	testExportConfig(t)
}

// testAdenATSConfig 测试亚丁ATS配置
func testAdenATSConfig(t *testing.T) {
	adenATS := config.GetAdenATSConfig()
	if adenATS == nil {
		t.Error("亚丁ATS配置为空")
		return
	}

	if adenATS.BaseURL == "" {
		t.Error("ATS基础URL为空")
	}

	if adenATS.WssURL == "" {
		t.<PERSON>rror("WebSocket URL为空")
	}

	if adenATS.Username == "" {
		t.Error("用户名为空")
	}

	if adenATS.ClientId == "" {
		t.Error("客户端ID为空")
	}

	if adenATS.Timeout <= 0 {
		t.Error("连接超时时间配置无效")
	}

	if adenATS.Heartbeat <= 0 {
		t.Error("心跳间隔配置无效")
	}

	t.Logf("亚丁ATS配置: %+v", adenATS)
}

// testDataProcessConfig 测试数据处理配置
func testDataProcessConfig(t *testing.T) {
	dataProcess := config.GetDataProcessConfig()
	if dataProcess == nil {
		t.Error("数据处理配置为空")
		return
	}

	if dataProcess.RawBufferSize <= 0 {
		t.Error("原始数据缓冲区大小配置无效")
	}

	if dataProcess.ParsedBufferSize <= 0 {
		t.Error("解析后数据缓冲区大小配置无效")
	}

	if dataProcess.WorkerNum <= 0 {
		t.Error("工作线程数配置无效")
	}

	if dataProcess.BatchSize <= 0 {
		t.Error("批处理大小配置无效")
	}

	if dataProcess.DataRetentionDays <= 0 {
		t.Error("数据保留天数配置无效")
	}

	t.Logf("数据处理配置: %+v", dataProcess)
}

// testExportConfig 测试文件导出配置
func testExportConfig(t *testing.T) {
	exportConfig := config.GetExportConfig()
	if exportConfig == nil {
		t.Error("文件导出配置为空")
		return
	}

	if exportConfig.Path == "" {
		t.Error("导出路径为空")
	}

	if exportConfig.URLPrefix == "" {
		t.Error("URL前缀为空")
	}

	if exportConfig.RetentionDays <= 0 {
		t.Error("文件保留天数配置无效")
	}

	t.Logf("文件导出配置: %+v", exportConfig)
}

// TestConfigValidation 测试配置验证
func TestConfigValidation(t *testing.T) {
	// 初始化配置
	config.InitFromLocalFile("config", "yaml")

	// 验证配置
	err := config.ValidateConfig()
	if err != nil {
		t.Errorf("配置验证失败: %v", err)
	}
}

// BenchmarkConfigAccess 配置访问性能测试
func BenchmarkConfigAccess(b *testing.B) {
	// 初始化配置
	config.InitFromLocalFile("config", "yaml")

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 测试配置访问性能
		_ = config.GetAdenATSConfig()
		_ = config.GetDataProcessConfig()
		_ = config.GetExportConfig()
	}
}
