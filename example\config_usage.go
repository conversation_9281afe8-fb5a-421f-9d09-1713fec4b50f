package main

import (
	"log"
	config "test/conf"
)

// 配置使用示例
func main() {
	// 1. 初始化配置（支持从nacos读取，降级到本地yaml）
	err := config.InitConfig("wealth-bond-quote-service")
	if err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	// 2. 验证配置有效性
	err = config.ValidateConfig()
	if err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 3. 打印配置摘要
	config.PrintConfigSummary()

	// 4. 使用各项配置
	useConfigurations()

	log.Println("配置使用示例完成")
}

// useConfigurations 展示如何使用各项配置
func useConfigurations() {
	// 使用亚丁ATS配置
	useAdenATSConfig()

	// 使用数据处理配置
	useDataProcessConfig()

	// 使用文件导出配置
	useExportConfig()
}

// useAdenATSConfig 使用亚丁ATS配置示例
func useAdenATSConfig() {
	log.Println("=== 亚丁ATS配置使用示例 ===")

	adenATS := config.GetAdenATSConfig()
	log.Printf("亚丁ATS配置: %+v", adenATS)

	// 在实际使用中，可以这样使用配置
	log.Printf("连接地址: %s", adenATS.BaseURL)
	log.Printf("WebSocket地址: %s", adenATS.WssURL)
	log.Printf("用户名: %s", adenATS.Username)
	log.Printf("客户端ID: %s", adenATS.ClientId)
	log.Printf("连接超时: %d秒", adenATS.Timeout)
	log.Printf("心跳间隔: %d毫秒", adenATS.Heartbeat)
}

// useDataProcessConfig 使用数据处理配置示例
func useDataProcessConfig() {
	log.Println("=== 数据处理配置使用示例 ===")

	dataProcess := config.GetDataProcessConfig()
	log.Printf("数据处理配置: %+v", dataProcess)

	// 在实际使用中，可以这样使用配置
	log.Printf("原始数据缓冲区大小: %d", dataProcess.RawBufferSize)
	log.Printf("解析后数据缓冲区大小: %d", dataProcess.ParsedBufferSize)
	log.Printf("死信队列缓冲区大小: %d", dataProcess.DeadBufferSize)
	log.Printf("数据处理工作线程数: %d", dataProcess.WorkerNum)
	log.Printf("数据解析工作线程数: %d", dataProcess.ParserWorkerNum)
	log.Printf("数据库写入工作线程数: %d", dataProcess.DbWorkerNum)
	log.Printf("批处理大小: %d", dataProcess.BatchSize)
	log.Printf("刷新延迟: %d毫秒", dataProcess.FlushDelayMs)
	log.Printf("数据保留天数: %d", dataProcess.DataRetentionDays)
	log.Printf("清理任务执行间隔: %d小时", dataProcess.CleanupIntervalHours)
}

// useExportConfig 使用文件导出配置示例
func useExportConfig() {
	log.Println("=== 文件导出配置使用示例 ===")

	exportConfig := config.GetExportConfig()
	log.Printf("文件导出配置: %+v", exportConfig)

	// 在实际使用中，可以这样使用配置
	log.Printf("导出路径: %s", exportConfig.Path)
	log.Printf("URL前缀: %s", exportConfig.URLPrefix)
	log.Printf("文件保留天数: %d", exportConfig.RetentionDays)
}
